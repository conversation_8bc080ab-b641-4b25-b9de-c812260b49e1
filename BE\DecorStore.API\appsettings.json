{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\MSSQLLocalDB;Database=DecorStoreDb;Trusted_Connection=True;TrustServerCertificate=True"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "JWT": {"SecretKey": "YourSuperSecretKey12345!@#$%ThisShouldBeVeryLongAndComplex", "Issuer": "DecorStore", "Audience": "DecorStoreClients", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ClockSkewMinutes": 5, "RequireHttpsMetadata": true, "SaveToken": true, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "EnableDebugEvents": false}, "ImageSettings": {"BasePath": "Uploads", "MaxFileSize": 10485760, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif"]}, "Cache": {"DefaultExpirationMinutes": 60, "LongTermExpiryMinutes": 1440, "ShortTermExpiryMinutes": 15, "EnableCaching": true, "EnableDistributedCache": false, "CacheKeyPrefix": "DecorStore", "MaxCacheSizeMB": 100, "DefaultSizeLimit": 1000000, "SlidingExpirationMinutes": 30, "CompressLargeValues": true, "CompressionThresholdBytes": 10240, "RedisDatabase": 0, "RedisTimeoutMs": 5000, "EnableCacheWarming": true, "CacheWarmupKeys": ["categories:all", "products:featured", "dashboard:stats"]}, "FileStorage": {"UploadPath": "Uploads", "ThumbnailPath": ".thumbnails", "MaxFileSizeMB": 10, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".webp"], "ThumbnailWidth": 200, "ThumbnailHeight": 200, "ImageQuality": 85, "EnableImageOptimization": true, "GenerateThumbnails": true, "MaxFilesPerDirectory": 1000, "UseSubdirectories": true, "BaseUrl": "/uploads", "ThumbnailUrl": "/.thumbnails"}, "Database": {"ConnectionString": "Server=(localdb)\\MSSQLLocalDB;Database=DecorStoreDb;Trusted_Connection=True;TrustServerCertificate=True", "MaxRetryCount": 5, "MaxRetryDelaySeconds": 30, "MigrationHistoryTable": "__EFMigrationsHistory", "EnableSensitiveDataLogging": false, "EnableDetailedErrors": true, "CommandTimeoutSeconds": 30}, "Api": {"DefaultVersion": "v1", "SupportedVersions": ["v1"], "RequestsPerMinute": 100, "BurstLimit": 200, "AllowedOrigins": ["https://localhost", "http://localhost"], "AllowedHeaders": ["Content-Type", "Authorization", "X-Correlation-ID"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowCredentials": true, "EnableSwagger": true, "SwaggerEndpoint": "/swagger/v1/swagger.json", "SwaggerTitle": "DecorStore API", "DefaultLogLevel": "Information", "MicrosoftLogLevel": "Warning", "EnableSensitiveDataLogging": false, "EnableDetailedErrors": true, "RequestTimeoutSeconds": 30, "MaxRequestBodySizeMB": 100, "EnableCompression": true, "EnableResponseCaching": true, "DefaultCacheDurationSeconds": 300}}