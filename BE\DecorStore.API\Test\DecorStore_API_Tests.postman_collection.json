{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "DecorStore API Tests", "description": "Comprehensive API testing collection for DecorStore with realistic test data", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🔐 Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has token and user\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('token');", "    pm.expect(jsonData).to.have.property('user');", "    ", "    // Save token and user info", "    pm.environment.set(\"authToken\", jsonData.token);", "    pm.environment.set(\"userId\", jsonData.user.id);", "    pm.environment.set(\"userEmail\", jsonData.user.email);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"johndoe\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\",\n  \"confirmPassword\": \"Password123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Auth/register", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "register"]}}}, {"name": "Register Admin User", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Admin user registered successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('token');", "    pm.expect(jsonData).to.have.property('user');", "    ", "    // Save admin info", "    pm.environment.set(\"adminUserId\", jsonData.user.id);", "    pm.environment.set(\"adminEmail\", jsonData.user.email);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"AdminPass123!\",\n  \"confirmPassword\": \"AdminPass123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Auth/register", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "register"]}}}, {"name": "Make User Admin", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"User role updated to Admin\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.role).to.eql('Admin');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "\"{{adminEmail}}\""}, "url": {"raw": "{{baseUrl}}/api/Auth/make-admin", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "make-admin"]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Admin login successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('token');", "    pm.expect(jsonData.user.role).to.eql('Admin');", "    ", "    // Save admin token", "    pm.environment.set(\"adminToken\", jsonData.token);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{adminEmail}}\",\n  \"password\": \"AdminPass123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Auth/login", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "login"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"User login successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('token');", "    pm.expect(jsonData).to.have.property('user');", "    ", "    // Update auth token for regular user", "    pm.environment.set(\"authToken\", jsonData.token);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{userEmail}}\",\n  \"password\": \"Password123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Auth/login", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "login"]}}}, {"name": "Get Current User", "event": [{"listen": "prerequest", "script": {"exec": ["pm.request.headers.add({", "    key: 'Authorization',", "    value: 'Bearer ' + pm.environment.get('authToken')", "});"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"User information returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('username');", "    pm.expect(jsonData).to.have.property('email');", "    pm.expect(jsonData).to.have.property('role');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Auth/user", "host": ["{{baseUrl}}"], "path": ["api", "<PERSON><PERSON>", "user"]}}}]}, {"name": "📂 Categories", "item": [{"name": "Create Root Category [Admin]", "event": [{"listen": "prerequest", "script": {"exec": ["pm.request.headers.add({", "    key: 'Authorization',", "    value: 'Bearer ' + pm.environment.get('adminToken')", "});"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Category created successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData.name).to.eql('Living Room');", "    ", "    // Save category ID for future tests", "    pm.environment.set(\"rootCategoryId\", jsonData.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Living Room\",\n  \"slug\": \"living-room\",\n  \"description\": \"Furniture and decor for living rooms\",\n  \"parentId\": null,\n  \"imageIds\": []\n}"}, "url": {"raw": "{{baseUrl}}/api/Category", "host": ["{{baseUrl}}"], "path": ["api", "Category"]}}}, {"name": "Create Bedroom Category [Admin]", "event": [{"listen": "prerequest", "script": {"exec": ["pm.request.headers.add({", "    key: 'Authorization',", "    value: 'Bearer ' + pm.environment.get('adminToken')", "});"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Bedroom category created\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.name).to.eql('Bedroom');", "    pm.environment.set(\"bedroomCategoryId\", jsonData.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Bedroom\",\n  \"slug\": \"bedroom\",\n  \"description\": \"Bedroom furniture and accessories\",\n  \"parentId\": null,\n  \"imageIds\": []\n}"}, "url": {"raw": "{{baseUrl}}/api/Category", "host": ["{{baseUrl}}"], "path": ["api", "Category"]}}}, {"name": "Create Subcategory - <PERSON><PERSON><PERSON> [Admin]", "event": [{"listen": "prerequest", "script": {"exec": ["pm.request.headers.add({", "    key: 'Authorization',", "    value: 'Bearer ' + pm.environment.get('adminToken')", "});"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Subcategory created successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.name).to.eql('Sofas');", "    pm.environment.set(\"subcategoryId\", jsonData.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>fas\",\n  \"slug\": \"sofas\",\n  \"description\": \"Comfortable sofas for your living room\",\n  \"parentId\": {{rootCategoryId}},\n  \"imageIds\": []\n}"}, "url": {"raw": "{{baseUrl}}/api/Category", "host": ["{{baseUrl}}"], "path": ["api", "Category"]}}}, {"name": "Get Categories (Paginated)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Paginated response structure\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('items');", "    pm.expect(jsonData).to.have.property('totalCount');", "    pm.expect(jsonData).to.have.property('page');", "    pm.expect(jsonData).to.have.property('pageSize');", "    pm.expect(jsonData.items).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Category?page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "Category"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}}, {"name": "Get All Categories", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Categories array returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "    pm.expect(jsonData.length).to.be.greaterThan(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Category/all", "host": ["{{baseUrl}}"], "path": ["api", "Category", "all"]}}}, {"name": "Get Category by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Category details returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('name');", "    pm.expect(jsonData).to.have.property('slug');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Category/{{rootCategoryId}}", "host": ["{{baseUrl}}"], "path": ["api", "Category", "{{rootCategoryId}}"]}}}, {"name": "Get Category by Slug", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Category found by slug\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('slug');", "    pm.expect(jsonData.slug).to.eql('living-room');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Category/slug/living-room", "host": ["{{baseUrl}}"], "path": ["api", "Category", "slug", "living-room"]}}}, {"name": "Get Hierarchical Categories", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Hierarchical structure returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Category/hierarchical", "host": ["{{baseUrl}}"], "path": ["api", "Category", "hierarchical"]}}}]}, {"name": "🛍️ Products", "item": [{"name": "Create Product - Modern Sofa [Admin]", "event": [{"listen": "prerequest", "script": {"exec": ["pm.request.headers.add({", "    key: 'Authorization',", "    value: 'Bearer ' + pm.environment.get('adminToken')", "});"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Product created successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData.name).to.eql('Modern Sofa');", "    pm.expect(jsonData.price).to.eql(899.99);", "    ", "    // Save product ID for future tests", "    pm.environment.set(\"productId\", jsonData.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Modern Sofa\",\n  \"slug\": \"modern-sofa\",\n  \"description\": \"Comfortable modern sofa perfect for any living room. Features premium fabric upholstery and sturdy wooden frame.\",\n  \"price\": 899.99,\n  \"originalPrice\": 1200.00,\n  \"stockQuantity\": 15,\n  \"sku\": \"SOFA-MOD-001\",\n  \"categoryId\": {{subcategoryId}},\n  \"isFeatured\": true,\n  \"isActive\": true,\n  \"imageIds\": []\n}"}, "url": {"raw": "{{baseUrl}}/api/Products", "host": ["{{baseUrl}}"], "path": ["api", "Products"]}}}, {"name": "Create Product - Coffee Table [Admin]", "event": [{"listen": "prerequest", "script": {"exec": ["pm.request.headers.add({", "    key: 'Authorization',", "    value: 'Bearer ' + pm.environment.get('adminToken')", "});"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Coffee table created successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.name).to.eql('Coffee Table');", "    pm.environment.set(\"productId2\", jsonData.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Coffee Table\",\n  \"slug\": \"coffee-table\",\n  \"description\": \"Elegant wooden coffee table with glass top. Perfect centerpiece for your living room.\",\n  \"price\": 299.99,\n  \"originalPrice\": 399.99,\n  \"stockQuantity\": 25,\n  \"sku\": \"TABLE-COF-001\",\n  \"categoryId\": {{rootCategoryId}},\n  \"isFeatured\": false,\n  \"isActive\": true,\n  \"imageIds\": []\n}"}, "url": {"raw": "{{baseUrl}}/api/Products", "host": ["{{baseUrl}}"], "path": ["api", "Products"]}}}, {"name": "Create Product - Bed Frame [Admin]", "event": [{"listen": "prerequest", "script": {"exec": ["pm.request.headers.add({", "    key: 'Authorization',", "    value: 'Bearer ' + pm.environment.get('adminToken')", "});"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Bed frame created successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.name).to.eql('King <PERSON>ze <PERSON>ame');", "    pm.environment.set(\"productId3\", jsonData.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"King Size Bed Frame\",\n  \"slug\": \"king-size-bed-frame\",\n  \"description\": \"Luxurious king size bed frame made from solid oak wood. Includes headboard and footboard.\",\n  \"price\": 1299.99,\n  \"originalPrice\": 1599.99,\n  \"stockQuantity\": 8,\n  \"sku\": \"BED-KING-001\",\n  \"categoryId\": {{bedroomCategoryId}},\n  \"isFeatured\": true,\n  \"isActive\": true,\n  \"imageIds\": []\n}"}, "url": {"raw": "{{baseUrl}}/api/Products", "host": ["{{baseUrl}}"], "path": ["api", "Products"]}}}, {"name": "Get Products (Paginated)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Paginated products response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('items');", "    pm.expect(jsonData).to.have.property('totalCount');", "    pm.expect(jsonData).to.have.property('page');", "    pm.expect(jsonData).to.have.property('pageSize');", "    pm.expect(jsonData.items).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Products?page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "Products"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}}, {"name": "Get Product by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Product details returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('name');", "    pm.expect(jsonData).to.have.property('price');", "    pm.expect(jsonData).to.have.property('categoryName');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["api", "Products", "{{productId}}"]}}}, {"name": "Get Featured Products", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Featured products returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Products/featured?count=5", "host": ["{{baseUrl}}"], "path": ["api", "Products", "featured"], "query": [{"key": "count", "value": "5"}]}}}, {"name": "Search Products with Filters", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Filtered products returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('items');", "    pm.expect(jsonData.items).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Products?searchTerm=sofa&minPrice=500&maxPrice=1000&isFeatured=true&page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "Products"], "query": [{"key": "searchTerm", "value": "sofa"}, {"key": "minPrice", "value": "500"}, {"key": "maxPrice", "value": "1000"}, {"key": "isFeatured", "value": "true"}, {"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}}, {"name": "Update Product [Admin]", "event": [{"listen": "prerequest", "script": {"exec": ["pm.request.headers.add({", "    key: 'Authorization',", "    value: 'Bearer ' + pm.environment.get('adminToken')", "});"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Modern Sofa - Updated\",\n  \"slug\": \"modern-sofa-updated\",\n  \"description\": \"Updated description: Comfortable modern sofa perfect for any living room. Features premium fabric upholstery and sturdy wooden frame. Now with improved cushioning!\",\n  \"price\": 949.99,\n  \"originalPrice\": 1200.00,\n  \"stockQuantity\": 12,\n  \"sku\": \"SOFA-MOD-001\",\n  \"categoryId\": {{subcategoryId}},\n  \"isFeatured\": true,\n  \"isActive\": true,\n  \"imageIds\": []\n}"}, "url": {"raw": "{{baseUrl}}/api/Products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["api", "Products", "{{productId}}"]}}}]}, {"name": "📦 Orders", "item": [{"name": "Create Order", "event": [{"listen": "prerequest", "script": {"exec": ["pm.request.headers.add({", "    key: 'Authorization',", "    value: 'Bearer ' + pm.environment.get('authToken')", "});"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Order created successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('totalAmount');", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('orderItems');", "    ", "    // Save order ID for future tests", "    pm.environment.set(\"orderId\", jsonData.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": {{userId}},\n  \"customerName\": \"<PERSON>\",\n  \"customerEmail\": \"<EMAIL>\",\n  \"customerPhone\": \"+1234567890\",\n  \"shippingAddress\": \"123 Main Street, Anytown, ST 12345\",\n  \"orderItems\": [\n    {\n      \"productId\": {{productId}},\n      \"quantity\": 1,\n      \"unitPrice\": 949.99\n    },\n    {\n      \"productId\": {{productId2}},\n      \"quantity\": 2,\n      \"unitPrice\": 299.99\n    }\n  ],\n  \"notes\": \"Please handle with care. Fragile items.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Order", "host": ["{{baseUrl}}"], "path": ["api", "Order"]}}}, {"name": "Get Order by ID", "event": [{"listen": "prerequest", "script": {"exec": ["pm.request.headers.add({", "    key: 'Authorization',", "    value: 'Bearer ' + pm.environment.get('authToken')", "});"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Order details returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('customerName');", "    pm.expect(jsonData).to.have.property('orderItems');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Order/{{orderId}}", "host": ["{{baseUrl}}"], "path": ["api", "Order", "{{orderId}}"]}}}, {"name": "Update Order Status [Admin]", "event": [{"listen": "prerequest", "script": {"exec": ["pm.request.headers.add({", "    key: 'Authorization',", "    value: 'Bearer ' + pm.environment.get('adminToken')", "});"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"Processing\",\n  \"notes\": \"Order is being prepared for shipment\"\n}"}, "url": {"raw": "{{baseUrl}}/api/Order/{{orderId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "Order", "{{orderId}}", "status"]}}}, {"name": "Get All Orders [Admin]", "event": [{"listen": "prerequest", "script": {"exec": ["pm.request.headers.add({", "    key: 'Authorization',", "    value: 'Bearer ' + pm.environment.get('adminToken')", "});"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Paginated orders returned\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('items');", "    pm.expect(jsonData).to.have.property('totalCount');", "    pm.expect(jsonData.items).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Order?page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["api", "Order"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}}]}, {"name": "❤️ Health Check", "item": [{"name": "Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Health check response\", function () {", "    var responseText = pm.response.text();", "    pm.expect(responseText).to.include('Healthy');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:5000", "type": "string"}]}